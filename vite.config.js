import {
	defineConfig
} from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import fs from 'node:fs'
const path = require('path');

function protobufPatch() {
	return {
		name: 'protobuf-patch',
		transform(code, id) {
			// https://github.com/protobufjs/protobuf.js/issues/1754
			if (id.endsWith('@protobufjs/inquire/index.js')) {
				return {
					code: code.replace(`eval("quire".replace(/^/,"re"))`, 'require'),
					map: null,
				};
			}
		},
	};
}

export default defineConfig({
	plugins: [uni(), protobufPatch()],
	server: {
		open: true,
		https: {
			// mkcert localhost *********** ************* ::1 创建证书
			cert: fs.readFileSync(path.resolve(__dirname, 'ssl/localhost+3.pem')),
			key: fs.readFileSync(path.resolve(__dirname, 'ssl/localhost+3-key.pem'))
		}
	}
});