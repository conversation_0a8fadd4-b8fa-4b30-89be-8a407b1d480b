<template>
  <network-monitor>
    <view class="home-container" @click="closeLanguageDropdown">
      <scroll-view class="main-scroll" scroll-y="true" @scrolltolower="handleScrollToLower" @scroll="handleScroll"
                   :scroll-with-animation="true" :enhanced="true" :show-scrollbar="false">
        <view class="scroll-content">
          <view class="header-section">
            <view class="header-content">
              <view class="language-btn" @click="openLanguageModal">
                <text class="language-code">{{ currentLang.toUpperCase() }}</text>
                <uni-icons type="down" size="12" color="#999999"></uni-icons>
              </view>

              <view class="location-selector" @click="openLocationPicker">
                <uni-icons type="location" size="16" color="#333333"></uni-icons>
                <text class="location-text">{{ $t(cityName) }}</text>
                <!--                  <uni-icons type="down" size="12" color="#999999"></uni-icons>-->
              </view>
            </view>
          </view>

          <!-- Banner Section -->
          <banner-swiper position-id="home"></banner-swiper>


          <!-- Live Stream Section -->
          <view class="section-header">
            <text class="section-title">{{ $t('正在直播') }}</text>
            <view class="section-action" @click="switchTab('/pages/live/index')">
              <text>{{ $t('查看全部') }}</text>
              <uni-icons type="right" size="12" color="#999999"></uni-icons>
            </view>
          </view>

          <view class="live-stream-section">
            <scroll-view class="live-scroll" scroll-x="true" show-scrollbar="false">
              <view class="live-list" v-if="liveRooms && liveRooms.length > 0">
                <live-card v-for="room in liveRooms" :key="room.id" :liveRoom="room"
                           class="live-card-horizontal"/>
              </view>
              <view class="empty-live" v-else>
                <uni-icons type="videocam" size="48" color="#999999"></uni-icons>
                <text class="empty-text">{{ $t('暂无直播') }}</text>
              </view>
            </scroll-view>
          </view>

          <!-- Recommended Stream Cards -->
          <view class="section-header">
            <text class="section-title">{{ $t('热门推荐') }}</text>
            <view class="section-action" @click="goToCategory(0)">
              <text>{{ $t('查看更多') }}</text>
              <uni-icons type="right" size="14" color="#1da1f2"></uni-icons>
            </view>
          </view>

          <view class="video-grid">
            <template v-if="recommendedVideos && recommendedVideos.length > 0">
              <view class="video-item" v-for="video in recommendedVideos" :key="video.id"
                    @click="goToVideo(video.id)">
                <view class="video-cover">
                  <image :src="video.coverUrl" mode="aspectFill"></image>
                  <view class="duration">{{ formatDuration(video.duration) }}</view>
                  <!--                  <view v-if="video.paymentType !== 'free'" class="payment-badge">-->
                  <!--                    <uni-icons type="locked" size="14" color="#fff"></uni-icons>-->
                  <!--                    <text>{{ video.paymentType === 'vip' ? 'VIP' : $t('付费') }}</text>-->
                  <!--                  </view>-->
                </view>
                <view class="video-info">
                  <text class="video-title">{{ video.title }}</text>
                  <view class="video-stats">
                    <view class="flex-row align-center">
                      <uni-icons type="eye" size="12" color="#999"></uni-icons>
                      <text class="ml-xs">{{ formatNumber(video.viewNum) }}</text>
                    </view>
                    <view class="flex-row align-center">
                      <uni-icons type="calendar" size="12" color="#999"></uni-icons>
                      <text class="ml-xs">{{ formatTimeAgo(video.createdAt) }}</text>
                    </view>
                  </view>
                  <view class="video-user">
                    <image :src="video.user.head" mode="aspectFill" class="user-avatar"
                           @click.stop="goToUser(video.user.userId)"></image>
                    <text @click.stop="goToUser(video.user.userId)">{{ video.user.nickname }}</text>
                  </view>
                </view>
              </view>
            </template>
            <view v-else class="empty-video">
              <uni-icons type="videocam" size="48" color="#999999"></uni-icons>
              <text class="empty-text">{{ $t('暂无推荐视频') }}</text>
            </view>
          </view>

          <!-- Popular Posts Section -->
          <view class="section-header">
            <text class="section-title">{{ $t('热门动态') }}</text>
            <view class="section-action" @click="switchTab('/pages/social/index')">
              <text>{{ $t('查看全部') }}</text>
              <uni-icons type="right" size="12" color="#999999"></uni-icons>
            </view>
          </view>

          <view class="popular-posts-section">
            <template v-if="popularPosts && popularPosts.length > 0">
              <social-post v-for="post in popularPosts" :key="post.id" :post="post"/>
            </template>
            <view v-else class="empty-posts">
              <uni-icons type="chat" size="48" color="#999999"></uni-icons>
              <text class="empty-text">{{ $t('暂无热门动态') }}</text>
            </view>
          </view>

          <!-- 底部提示区域 -->
          <view class="bottom-tip" :class="{ 'show-tip': showBottomTip }">
            <view class="tip-content">
              <uni-icons type="up" size="16" color="#999999"></uni-icons>
              <text class="tip-text">{{ $t('继续上拉进入社区') }}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 悬浮客服按钮 -->
      <view class="floating-support-button" @click="goToSupport">
        <uni-icons type="headphones" size="20" color="#FFFFFF"></uni-icons>
      </view>
    </view>

    <!-- 语言选择弹窗 -->
    <view class="language-modal-overlay" v-if="showLanguageModal" @click="closeLanguageModal">
      <view class="language-modal" @click.stop>
        <view class="language-modal-header">
          <text class="language-modal-title">{{ $t('选择语言') }}</text>
          <uni-icons type="closeempty" size="20" color="#333333" @click="closeLanguageModal"></uni-icons>
        </view>
        <view class="language-modal-content">
          <view
              v-for="lang in languages"
              :key="lang.code"
              class="language-modal-item"
              :class="{ active: currentLang === lang.code }"
              @click="switchLanguage(lang.code)"
          >
            <text>{{ lang.name }}</text>
            <uni-icons v-if="currentLang === lang.code" type="checkmarkempty" size="18" color="#FF5A5F"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </network-monitor>
</template>

<script>
import LiveCard from '../../components/LiveCard.vue';
import SocialPost from '../../components/SocialPost.vue';
import {
  AppsLiveApiLiveRoomsApiLists
} from '@/network/api/live-rooms'
import {
  AppsLiveApiLiveVideosApiLists
} from "@/network/api/live-videos";
import {
  AppsLiveApiLivePostsApiLists
} from '@/network/api/live-posts';
import {
  User
} from "@/Lib/User";
import BannerSwiper from "@/components/BannerSwiper.vue";
import NetworkMonitor from "@/components/NetworkMonitor.vue";
import i18n from "@/Lib/i18n";
import {
  AppCommonCtrlUserPositionApiIpLocation,
  AppCommonCtrlUserPositionApiSaveLocation
} from "@/network/common/user-position-api";
import {
  ChatMessage
} from "@/utils/ChatMessage";

export default {
  components: {
    NetworkMonitor,
    BannerSwiper,
    LiveCard,
    SocialPost
  },

  data() {
    return {
      // User data
      userCoins: 0,

      // Live rooms data
      liveRooms: [],
      recommendedVideos: [],
      cityName: '城市名称',
      // Social posts data
      popularPosts: [],
      showBottomTip: false,
      scrollTop: 0,
      lastScrollTop: 0,
      isScrolling: false,
      showLanguageModal: false,
      languages: [
        {
          code: 'vi',
          name: 'Tiếng Việt'
        },
        {
          code: 'en',
          name: 'English'
        }, {
          code: 'zh',
          name: '中文'
        }

      ],
      currentLang: i18n.currentLang,
    }
  },

  onLoad() {
    this.loadLiveRooms();
    this.loadPopularPosts();

    console.log('uni.getSystemInfo')
    uni.getSystemInfo({
      success(res) {
        console.log(res)
      }
    })
  },
  onShow() {
    i18n.updatePageTitle('首页')
    i18n.updateTabBarText()

    if (User.getUserId()) {
      this.getPos()
      ChatMessage.updateUnRead()
    }
  },
  onReachBottom() {
    // In a real app, load more data here
    console.log('Load more data');
  },

  methods: {
    openLanguageModal() {
      this.showLanguageModal = true;
    },

    closeLanguageModal() {
      this.showLanguageModal = false;
    },

    getPos() {
      uni.getLocation({
        type: 'wgs84',
        geocode: true,
        success: (res) => {
          console.log('当前位置的经度：' + res.longitude);
          console.log('当前位置的纬度：' + res.latitude);
          console.log('当前位置的纬度：' + JSON.stringify(res));
          if (res.address) {
            const addr = res.address
            AppCommonCtrlUserPositionApiSaveLocation({
              city: addr.city,
              country: addr.country,
              district: addr.district,
              street: addr.street,
              region: addr.province,
            }).then(pos => {
              this.getCityName()
            })
          } else {
            this.getCityName()
          }
        },
        fail: () => {
          console.log('pos error')
          this.getCityName()
        }
      });
    },
    getCityName(pos) {
      console.log("get ip pos")
      AppCommonCtrlUserPositionApiIpLocation(null, false).then(pos => {
        console.log("get ip pos:res:" + JSON.stringify(pos))
        if (pos.city) {
          this.cityName = pos.city
        } else if (pos.city) {
          this.cityName = pos.region
        } else if (pos.country) {
          this.cityName = pos.country
        }
      })
    },
    // Load live rooms data
    async loadLiveRooms() {
      try {
        // 获取正在直播的房间
        const liveRes = await AppsLiveApiLiveRoomsApiLists({
          page: 1,
          pageSize: 10,
          status: 2 // 直播中
        })
        if (liveRes && liveRes.lists) {
          this.liveRooms = liveRes.lists
        } else {
          this.liveRooms = []
        }

        // 获取热门推荐的视频
        const recommendedParams = {
          page: 1,
          pageSize: 4,
          ext: {
            sortField: 'LIKES',
            sortOrder: 'DESC'
          }
        };
        const recommendedRes = await AppsLiveApiLiveVideosApiLists(recommendedParams);
        if (recommendedRes && recommendedRes.lists) {
          this.recommendedVideos = recommendedRes.lists;
        } else {
          this.recommendedVideos = [];
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        this.liveRooms = []
        this.recommendedVideos = []
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
      }
    },

    // Load popular posts
    async loadPopularPosts() {
      try {
        const params = {
          ext: {
            page: 1,
            size: 3,
            extStr: 'recommend'
          },
          status: 1,
          userId: User.getUserId()
        };

        const res = await AppsLiveApiLivePostsApiLists(params);
        if (res && res.lists) {
          this.popularPosts = res.lists;
        } else {
          this.popularPosts = [];
        }
      } catch (error) {
        console.error('获取热门动态失败:', error);
        this.popularPosts = [];
        uni.showToast({
          title: '获取热门动态失败',
          icon: 'none'
        });
      }
    },

    // Format stream start time
    formatStartTime(timeString) {
      const date = new Date(timeString);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `今日 ${hours}:${minutes} 开播`;
    },


    // Format video duration
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // Format time ago
    formatTimeAgo(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
        if (diffHours === 0) {
          const diffMinutes = Math.floor(diffTime / (1000 * 60));
          return this.$t(`%s分钟前`, diffMinutes);
          // return `${diffMinutes}分钟前`;
        }
        return this.$t(`%s小时前`, diffHours);
        // return `${diffHours}小时前`;
      } else if (diffDays < 30) {
        return this.$t(`%s天前`, diffDays);
        // return `${diffDays}天前`;
      } else {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${month}-${day}`;
      }
    },

    // Format number
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + this.$t("万");
      }
      return num.toString();
    },

    // Navigate to video detail
    goToVideo(videoId) {
      uni.navigateTo({
        url: `/pages/video/detail?id=${videoId}`
      });
    },

    // Navigate to user profile
    goToUser(userId) {
      uni.navigateTo({
        url: `/pages/user/index?id=${userId}`
      });
    },

    // Navigate to category
    goToCategory(categoryId) {
      uni.navigateTo({
        url: `/pages/video/list?categoryId=${categoryId}`
      });
    },

    // Open location picker
    openLocationPicker() {
      uni.showToast({
        title: '此功能在演示版本中未实现',
        icon: 'none'
      });
    },
    switchTab(url) {
      uni.switchTab({
        url
      })
    },

    handleScroll(e) {
      this.scrollTop = e.detail.scrollTop;
      if (this.scrollTop > this.lastScrollTop) {
        // 向下滚动
        this.showBottomTip = false;
      } else if (this.scrollTop < this.lastScrollTop) {
        // 向上滚动
        if (this.scrollTop < 100) {
          this.showBottomTip = true;
        }
      }
      this.lastScrollTop = this.scrollTop;
    },

    handleScrollToLower() {
      if (!this.isScrolling) {
        this.isScrolling = true;
        this.showBottomTip = true;

        // 延迟执行切换，给用户一个回弹的视觉反馈
        setTimeout(() => {
          this.switchTab('/pages/social/index');
          this.isScrolling = false;
        }, 500);
      }
    },

    switchLanguage(lang) {
      this.currentLang = lang;
      i18n.setLanguage(lang)
      this.closeLanguageModal();
      // 这里可以添加切换语言的逻辑
      uni.showToast({
        title: `已切换至${this.languages.find(l => l.code === lang).name}`,
        icon: 'none'
      });
    },

    goToSupport() {
      uni.navigateTo({url: '/pages/support/index'});
    },
  }
}
</script>

<style scoped lang="scss">
.home-container {
  flex-direction: column;
  background-color: $uni-bg-color-grey;
  /* #ifndef APP-NVUE */
  display: flex;
  min-height: 100vh;
  /* #endif */
  /* #ifdef APP-NVUE */
  flex: 1;
  /* #endif */
  padding-bottom: 80rpx;
}

.header-section {
  background-color: $uni-bg-color;
  padding: $uni-spacing-col-xs 0;
}

.header-scroll {
  white-space: nowrap;
  width: 100%;
}

.header-content {
  display: inline-flex;
  align-items: center;
}

/* 统一选择器样式 */
.selector {
  display: inline-flex;
  align-items: center;
  background-color: $uni-bg-color-grey;
  border-radius: $uni-border-radius-pill;
  padding: $uni-spacing-col-xs $uni-spacing-row-base;
  margin-right: $uni-spacing-row-base;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
  }
}

.language-btn {
  display: flex;
  align-items: center;
  background-color: $uni-bg-color-grey;
  border-radius: $uni-border-radius-pill;
  padding: $uni-spacing-col-xs $uni-spacing-row-base;
  margin-right: $uni-spacing-row-base;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
  }
}

.language-code {
  font-size: $uni-font-size-sm;
  font-weight: 500;
  margin-right: $uni-spacing-row-xs;
}

.location-selector {
  @extend .selector;
  background-color: $uni-bg-color;

  .location-text {
    font-size: $uni-font-size-sm;
    margin: 0 $uni-spacing-row-xs;
    color: $uni-text-color;
    font-weight: 500;
  }
}

/* Category tabs */
.category-tabs {
  white-space: nowrap;
  background-color: #ffffff;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.category-tab {
  display: inline-block;
  padding: 6px 16px;
  margin: 0 4px;
  font-size: 14px;
  border-radius: 16px;
  color: #666;
  transition: all 0.2s;
}

.category-tab.active {
  background-color: $app-primary-color;
  color: white;
  font-weight: 500;
}

.category-tab:first-child {
  margin-left: 16px;
}

.category-tab:last-child {
  margin-right: 16px;
}

/* Banner Section */
.banner-swiper {
  height: 320rpx;
  margin: $uni-spacing-row-base;
}

.banner-item {
  height: 100%;
  border-radius: $uni-border-radius-base;
  overflow: hidden;
  position: relative;
}

.banner-image {
  /* #ifndef APP-NVUE */
  width: 100%;
  /* #endif */
  /* #ifdef APP-NVUE */
  flex: 1;
  /* #endif */
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* #ifndef APP-NVUE */
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
  /* #endif */
  /* #ifdef APP-NVUE */
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
  /* #endif */
}

.banner-title {
  position: absolute;
  bottom: $uni-spacing-col-lg;
  left: $uni-spacing-row-lg;
  color: $uni-text-color-inverse;
  font-size: $uni-font-size-lg;
  font-weight: bold;
}

/* Section Headers */
.section-header {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: $uni-spacing-col-lg $uni-spacing-row-lg $uni-spacing-col-base;
}

.section-title {
  font-size: $uni-font-size-lg;
  font-weight: bold;
  color: $uni-text-color;
}

.section-action {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;

  text {
    margin-right: $uni-spacing-row-xs;
  }
}

/* Live Stream Section */
.live-stream-section {
  padding: $uni-spacing-col-base $uni-spacing-row-base;
  background-color: $uni-bg-color;
  margin-bottom: $uni-spacing-col-base;
}

.live-scroll {
  /* #ifndef APP-NVUE */
  white-space: nowrap;
  /* #endif */
}

.live-list {
  /* #ifndef APP-NVUE */
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $uni-spacing-row-base;
  /* #endif */
  /* #ifdef APP-NVUE */
  flex-direction: row;
  flex-wrap: wrap;
  /* #endif */
}

.live-card-horizontal {
  width: 100%;
  background-color: $uni-bg-color;
  /* #ifndef APP-NVUE */
  box-shadow: $uni-shadow-sm;
  /* #endif */
  /* #ifdef APP-NVUE */
  elevation: 2;
  /* #endif */
  border-radius: $uni-border-radius-base;
  overflow: hidden;
}

.empty-live {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $uni-spacing-col-xl $uni-spacing-row-xl;
  background-color: $uni-bg-color;
  border-radius: $uni-border-radius-base;
  height: 320rpx;
  /* #ifndef APP-NVUE */
  box-shadow: $uni-shadow-sm;
  /* #endif */
  /* #ifdef APP-NVUE */
  elevation: 2;
  /* #endif */
}

.empty-text {
  margin-top: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
  color: $uni-text-color-grey;
}

/* Recommended Section */
.video-grid {
  padding: 0 $uni-spacing-row-base;
  /* #ifndef APP-NVUE */
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $uni-spacing-row-base;
  /* #endif */
  /* #ifdef APP-NVUE */
  flex-direction: row;
  flex-wrap: wrap;
  /* #endif */
}

.video-item {
  background-color: $uni-bg-color;
  border-radius: $uni-border-radius-base;
  overflow: hidden;
  margin-bottom: $uni-spacing-row-base;
  /* #ifndef APP-NVUE */
  box-shadow: $uni-shadow-sm;
  /* #endif */
  /* #ifdef APP-NVUE */
  elevation: 2;
  /* #endif */
  width: 100%;
}

.video-cover {
  position: relative;
  width: 100%;
}

.video-cover image {
  width: 100%;
  aspect-ratio: 9/16;
  object-fit: cover;
}

.duration {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  color: $uni-text-color-inverse;
  font-size: $uni-font-size-sm;
}

.payment-badge {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  color: $uni-text-color-inverse;
  font-size: $uni-font-size-sm;
}

.video-info {
  padding: $uni-spacing-row-sm;
}

.video-title {
  font-size: $uni-font-size-sm;
  font-weight: 500;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-col-xs;
  /* #ifndef APP-NVUE */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* #endif */
  /* #ifdef APP-NVUE */
  lines: 2;
  text-overflow: ellipsis;
  /* #endif */
}

.video-stats {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $uni-spacing-col-xs;
}

.flex-row {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
}

.ml-xs {
  margin-left: $uni-spacing-row-xs;
}

.video-user {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
  margin-top: $uni-spacing-col-xs;
}

.user-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: $uni-border-radius-circle;
  margin-right: $uni-spacing-row-xs;
}

/* Popular Posts Section */
.popular-posts-section {
  padding: 0 $uni-spacing-row-base;
}

/* Floating Button */
.floating-button {
  position: fixed;
  right: $uni-spacing-row-xl;
  bottom: 160rpx;
  background-color: $app-primary-color;
  width: 160rpx;
  height: 72rpx;
  border-radius: 36rpx;
  /* #ifndef APP-NVUE */
  display: flex;
  box-shadow: $uni-shadow-brand;
  /* #endif */
  /* #ifdef APP-NVUE */
  elevation: 6;
  /* #endif */
  flex-direction: row;
  align-items: center;
  justify-content: center;
  z-index: 100;

  text {
    margin-left: $uni-spacing-row-xs;
    font-size: $uni-font-size-base;
    color: $uni-text-color-inverse;
  }
}

/* Empty States */
.empty-video,
.empty-posts {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $uni-spacing-col-xl $uni-spacing-row-xl;
  background-color: $uni-bg-color;
  border-radius: $uni-border-radius-base;
  margin: $uni-spacing-col-base;
  /* #ifndef APP-NVUE */
  box-shadow: $uni-shadow-sm;
  /* #endif */
  /* #ifdef APP-NVUE */
  elevation: 2;
  /* #endif */
}

.empty-video {
  height: 320rpx;
}

.empty-posts {
  height: 240rpx;
}

.empty-text {
  margin-top: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
  color: $uni-text-color-grey;
}

.main-scroll {
  height: 100vh;
  /* #ifdef H5 */
  height: calc(100vh - var(--window-bottom));
  /* #endif */
}

.scroll-content {
  padding-bottom: 120rpx;
}

.bottom-tip {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  z-index: 10;
}

.bottom-tip.show-tip {
  transform: translateY(0);
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tip-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
  margin-top: 8rpx;
}

.webrtc-section {
  padding: $uni-spacing-col-base $uni-spacing-row-base;
  background-color: $uni-bg-color;
  margin-bottom: $uni-spacing-col-base;
}

.webrtc-button {
  background-color: $app-primary-color;
  padding: $uni-spacing-col-xs $uni-spacing-row-base;
  border-radius: $uni-border-radius-base;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  text {
    margin-left: $uni-spacing-row-xs;
    font-size: $uni-font-size-base;
    color: $uni-text-color-inverse;
  }
}

.webrtc-text {
  font-size: $uni-font-size-sm;
  font-weight: 500;
  color: $uni-text-color-inverse;
}

/* 语言选择弹窗 */
.language-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-modal {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  /* #ifndef APP-NVUE */
  animation: modalFadeIn 0.2s ease-out;
  /* #endif */
}

/* #ifndef APP-NVUE */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* #endif */

.language-modal-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.language-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.language-modal-content {
  max-height: 60vh;
  overflow-y: auto;
}

.language-modal-item {
  padding: 28rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f8f8f8;

  &:active {
    background-color: #f8f8f8;
  }

  &.active {
    color: #FF5A5F;
  }
}

/* 悬浮客服按钮 */
.floating-support-button {
  position: fixed;
  right: $uni-spacing-row-xl;
  bottom: 120rpx;
  background-color: $app-primary-color;
  color: $uni-text-color-inverse;
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* #ifndef APP-NVUE */
  box-shadow: $uni-shadow-brand;
  /* #endif */
  /* #ifdef APP-NVUE */
  elevation: 6;
  /* #endif */
  z-index: 100;
}
</style>