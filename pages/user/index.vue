<template>
  <z-paging ref="paging" v-model="dataList" @query="queryList" :default-page-size="10" :auto="false">
    <view class="container py-base bg-light-grey">
      <!-- 顶部个人信息卡片 -->
      <view class="card mb-base">
        <view class="profile-header">
          <view class="flex-row align-center">
            <image class="user-avatar radius-circle" :src="user.avatar || '/static/icons/usr.png'"
                   mode="aspectFill"></image>
            <view class="user-info ml-base">
              <view class="flex-row align-center">
                <text class="font-lg font-bold">{{ user.nickname }}</text>
                <view class="membership-badge ml-sm">
                  <text class="font-xs text-light">VIP {{ user.memberName }}</text>
                </view>
              </view>
              <view class="user-id font-sm text-grey mt-xs">ID: {{ user.id }}</view>
              <view class="live-status" v-if="isLive">
                <text class="font-xs text-light bg-error radius-pill px-sm py-xs mt-xs live-status-clickable" @click="enterLiveRoom">{{ $t('直播中') }}</text>
              </view>
            </view>
          </view>

          <view class="profile-actions mt-lg">
            <view class="action-btn btn-outline-primary radius-pill" :class="{'btn-primary': isFollowing}"
                  @click="toggleFollow">
              <text>{{ isFollowing ? $t('已关注') : $t('关注') }}</text>
            </view>
            <view class="action-btn btn-outline-secondary radius-pill ml-base" @click="sendMessage">
              <text>{{ $t('私信') }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 用户统计信息卡片 -->
      <view class="card mb-base">
        <view class="user-stats flex-row">
          <!-- <view class="stat-item flex-1 text-center">
            <text class="stat-value font-lg font-bold">{{user.level}}</text>
            <text class="stat-label font-sm text-grey">等级</text>
          </view> -->
          <view class="stat-item flex-1 text-center">
            <text class="stat-value font-lg font-bold">{{ user.followers }}</text>
            <text class="stat-label font-sm text-grey">{{ $t('粉丝') }}</text>
          </view>
          <view class="stat-item flex-1 text-center">
            <text class="stat-value font-lg font-bold">{{ user.following }}</text>
            <text class="stat-label font-sm text-grey">{{ $t('关注') }}</text>
          </view>
        </view>
      </view>

      <!-- 个人简介卡片 -->
      <view class="card mb-base">
        <view class="section-title font-bold mb-sm">{{ $t('个人简介') }}</view>
        <view class="section-content">
          <text class="description">{{ user.description || $t('这个人很懒，什么都没留下~') }}</text>
        </view>
      </view>

      <!-- 联系方式卡片 -->
      <view class="card mb-base">
        <view class="section-title font-bold mb-sm">{{ $t('联系方式') }}</view>
        <view class="section-content">
          <view class="contact-item flex-row justify-between align-center py-sm">
            <view class="flex-row align-center">
              <uni-icons type="phone" size="20" color="#666"></uni-icons>
              <text class="ml-base">{{ $t('手机号码') }}</text>
            </view>
            <text class="text-grey">{{ formatPhone(userContact.phone) }}</text>
          </view>
          <view class="divider"></view>
          <view class="contact-item flex-row justify-between align-center py-sm">
            <view class="flex-row align-center">
              <uni-icons type="weixin" size="20" color="#07C160"></uni-icons>
              <text class="ml-base">{{ $t('微信') }}</text>
            </view>
            <text class="text-grey">{{ userContact.wechat ? $t('已绑定') : $t('未绑定') }}</text>
          </view>
          <view class="divider"></view>
          <view class="contact-item flex-row justify-between align-center py-sm">
            <view class="flex-row align-center">
              <uni-icons type="weibo" size="20" color="#E6162D"></uni-icons>
              <text class="ml-base">{{ $t('微博') }}</text>
            </view>
            <text class="text-grey">{{ userContact.weibo ? $t('已绑定') : $t('未绑定') }}</text>
          </view>
        </view>
      </view>

      <!-- 内容标签页 -->
      <view class="card">
        <view class="tabs-header flex-row">
          <view
              v-for="(tab, index) in tabs"
              :key="index"
              class="tab-item flex-1 text-center py-sm"
              :class="{'active': currentTab === index}"
              @click="switchTab(index)"
          >
            <text class="font-md">{{ tab.name }}</text>
          </view>
        </view>

        <!-- 内容列表区域 -->


        <view v-if="dataList && dataList.length === 0" class="empty-content text-center py-xl">
          <uni-icons type="info" size="32" color="#ccc"></uni-icons>
          <text class="text-grey d-block mt-sm">{{ $t('暂无帖子') }}</text>
        </view>

        <!-- 帖子列表 -->
        <template v-if="currentTab === 0">
          <view v-for="(post, index) in dataList" :key="index" class="post-item p-base" @click="$navigateTo(`/pages/social/detail?id=${post.id}`)">
            <view class="post-title font-lg font-bold">{{ post.title }}</view>
            <view class="post-summary text-ellipsis-3 mt-sm">{{ post.summary }}</view>
            <view class="post-meta flex-row align-center justify-between mt-sm">
              <view class="post-time text-grey font-sm">{{ post.createdAt }}</view>
              <view class="post-stats flex-row">
                <view class="stat-item flex-row align-center ml-md">
                  <uni-icons type="eye" size="16" color="#999"></uni-icons>
                  <text class="font-sm text-grey ml-xs">{{ post.viewNum }}</text>
                </view>
                <view class="stat-item flex-row align-center ml-md">
                  <uni-icons type="star" size="16" color="#999"></uni-icons>
                  <text class="font-sm text-grey ml-xs">{{ post.collectNum }}</text>
                </view>
                <view class="stat-item flex-row align-center ml-md">
                  <uni-icons type="chat" size="16" color="#999"></uni-icons>
                  <text class="font-sm text-grey ml-xs">{{ post.commentNum }}</text>
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 视频列表 -->
        <template v-if="currentTab === 1">
          <view class="video-grid flex-row flex-wrap">
            <view
                class="video-item"
                v-for="video in dataList"
                :key="video.id"
                @click="viewVideo(video.id)"
            >
              <view class="video-cover-container">
                <image class="video-cover radius-base" :src="video.coverUrl" mode="aspectFill"></image>
                <view class="video-duration radius-xs">{{ formatDuration(video.duration) }}</view>
              </view>
              <view class="video-title text-ellipsis-2 font-sm mt-xs">{{ video.title }}</view>
              <view class="video-stats flex-row align-center justify-between font-xs text-grey mt-xs">
                <text>{{ formatNumber(video.viewNum) }}{{ $t('观看') }}</text>
                <view class="flex-row">
                  <view class="mr-sm flex-row align-center">
                    <uni-icons type="hand-up" size="14" color="#999"></uni-icons>
                    <text class="ml-xs">{{ video.likes }}</text>
                  </view>
                  <view class="flex-row align-center">
                    <uni-icons type="chat" size="14" color="#999"></uni-icons>
                    <text class="ml-xs">{{ video.comments }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>

      </view>
    </view>
  </z-paging>
</template>

<script>
import SocialPost from '@/components/SocialPost.vue';
import {AppsLiveApiLiveUserApiDetailByUserId} from '@/network/api/live-user';
import {AppsLiveApiLivePostsApiListsByUser} from '@/network/api/live-posts';
import {AppsLiveApiLiveVideosApiListsByUser} from '@/network/api/live-videos';
import {AppCommonCtrlChatSessionApiCreate} from '@/network/common/chat-session-api';
import {AppsLiveApiLiveRoomsApiDetailByUserId} from '@/network/api/live-rooms';
import { User } from '@/Lib/User';
import i18n from "@/Lib/i18n";
import { checkFollowStatus, toggleUserFollow } from '@/utils/followUtils';

export default {
  components: {
    SocialPost
  },
  data() {
    return {
      userId: null,
      user: {},
      isFollowing: false,
      isLive: false,

      currentTab: 0,
      dataList: [],
      userContact: {
        phone: '',
        wechat: false,
        weibo: false,
        qq: false
      }
    };
  },
  computed: {
    tabs() {
      return [
        {name: this.$t('最新帖子'), type: 'posts'},
        {name: this.$t('发布的视频'), type: 'videos'}
      ]
    },
  },
  onShow() {
    i18n.updatePageTitle("信息")
  },
  onLoad(option) {
    if (option.id) {
      this.userId = option.id;
    }
    this.loadUserData();
  },
  methods: {
    async loadUserData() {
      try {
        // 使用API获取用户基本信息
        const userData = await AppsLiveApiLiveUserApiDetailByUserId({
          userId: this.userId,
        });
        console.log(userData)
        if (userData) {
          // 适配LiveUserProto数据结构
          this.user = {
            id: userData.userId,
            username: userData.memberIdStr || '',
            nickname: userData.nickname || '',
            avatar: userData.head || '',
            description: userData.bio || '',
            memberName: userData.member.name || '',
            level: 1, // 默认等级
            followers: userData.fansCount || 0,
            following: userData.followCount || 0
          };

          // 设置联系方式
          this.userContact = {
            phone: userData.ext.phone || '', // 手机号可能需要从其他接口获取
            wechat: userData.wechat === 1,
            weibo: userData.weibo === 1,
            qq: userData.qq === 1
          };

          // 检查用户是否在直播
          this.isLive = userData.isLive || false;

          // 检查关注状态
          const isFollowed = await checkFollowStatus(this.userId);
          this.isFollowing = isFollowed;

          // 初始化z-paging并加载第一页数据
          this.$nextTick(() => {
            this.$refs.paging && this.$refs.paging.reload();
          });
        }
      } catch (error) {
        console.error('获取用户数据失败', error);
        uni.showToast({
          title: this.$t('获取用户信息失败'),
          icon: 'none'
        });
      }
    },

    // 切换标签页
    switchTab(index) {
      if (this.currentTab !== index) {
        this.currentTab = index;
        // 切换标签时重新加载列表
        this.$nextTick(() => {
          this.$refs.paging && this.$refs.paging.reload();
        });
      }
    },

    // 列表查询方法，根据当前标签页类型调用不同的API
    async queryList(pageNo, pageSize) {
      try {
        const params = {
          userId: this.userId || this.user.id,
          page: pageNo,
          pageSize: pageSize
        };

        if (this.currentTab === 0) {
          // 加载帖子列表
          const res = await AppsLiveApiLivePostsApiListsByUser(params);
          const lists = res.lists || [];
          this.$refs.paging.complete(lists);
        } else {
          // 加载视频列表
          const res = await AppsLiveApiLiveVideosApiListsByUser(params);
          const lists = res.lists || [];
          this.$refs.paging.complete(lists);
        }
      } catch (error) {
        console.error('加载列表数据失败', error);
        this.$refs.paging.complete(false);
        uni.showToast({
          title: this.currentTab === 0 ? this.$t('获取帖子列表失败') : this.$t('获取视频列表失败'),
          icon: 'none'
        });
      }
    },

    // 格式化手机号码，保护隐私
    formatPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    // 关注/取消关注
    async toggleFollow() {
      // 检查用户是否已登录
      const currentUserId = User.getUserId();
      if (!currentUserId) {
        uni.navigateTo({
          url: '/pages/login/index'
        });
        return;
      }

      if (!this.userId) return;

      // 调用工具函数切换关注状态
      const result = await toggleUserFollow(this.userId, this.isFollowing);
      
      if (result.success) {
        this.isFollowing = result.newStatus;
        
        // 更新关注数量统计
        if (result.newStatus) {
          this.user.followers = (this.user.followers || 0) + 1;
        } else {
          this.user.followers = Math.max(0, (this.user.followers || 0) - 1);
        }
      }
    },

    // 发送私信
    sendMessage() {
      console.log("sendMessage:userId:" + this.userId)
      // 调用创建会话API
      AppCommonCtrlChatSessionApiCreate({
        ext: {
          userId: this.userId
        }
      }).then(res => {
        // 跳转到聊天页面，传入会话ID
        uni.navigateTo({
          url: `/pages/message/chat?sessionId=${res.id}`
        });
      }).catch(err => {
        uni.showToast({
          title: this.$t('创建聊天失败'),
          icon: 'none'
        });
      });
    },

    // 查看视频详情
    viewVideo(videoId) {
      uni.navigateTo({
        url: `/pages/video/detail?id=${videoId}`
      });
    },

    // 格式化视频时长
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + this.$t('万');
      }
      return num.toString();
    },

    // 进入直播间
    async enterLiveRoom() {
      try {
        // 调用获取直播间详情接口
        const roomData = await AppsLiveApiLiveRoomsApiDetailByUserId({
          userId: this.userId
        });
        
        if (roomData && roomData.id) {
          // 跳转到直播间页面
          uni.navigateTo({
            url: `/pages/live/room?roomId=${roomData.id}`
          });
        } else {
          uni.showToast({
            title: this.$t('直播间信息获取失败'),
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取直播间信息失败', error);
        uni.showToast({
          title: this.$t('进入直播间失败'),
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style>
.container {
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 1px 8px 1px rgba(165, 165, 165, 0.2);
}

.user-avatar {
  width: 150rpx;
  height: 150rpx;
}

.membership-badge {
  background-color: #FF5A5F;
  border-radius: 30rpx;
  padding: 4rpx 12rpx;
}

.profile-actions {
  display: flex;
  flex-direction: row;
}

.post-stats {
  gap: 20rpx;
}

.action-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70rpx;
  min-width: 160rpx;
  padding: 0 30rpx;
}

.user-stats {
  padding: 20rpx 0;
}

.section-title {
  font-size: 32rpx;
  color: #333;
}

.description {
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}

.contact-item {
  height: 90rpx;
}

.divider {
  height: 1rpx;
  background-color: #f0f0f0;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tabs-header {
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.tab-item {
  position: relative;
}

.tab-item.active {
  color: #007AFF;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}

.post-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.post-summary {
  font-size: 28rpx;
  line-height: 1.5;
  color: #666;
  max-height: 126rpx;
}

.video-grid {
  margin: -10rpx;
}

.video-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
}

.video-cover-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
}

.video-cover {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
}

.video-title {
  font-size: 26rpx;
  max-height: 70rpx;
  line-height: 1.3;
}

.d-block {
  display: block;
}

.live-status-clickable {
  cursor: pointer;
  transition: opacity 0.2s;
}

.live-status-clickable:hover {
  opacity: 0.8;
}
</style>
