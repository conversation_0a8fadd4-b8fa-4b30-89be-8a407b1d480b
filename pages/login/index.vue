<template>
  <view class="login-container">
    <!-- Logo and header -->
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">{{ $t('直播社交') }}</text>
      <text class="slogan">{{ $t('连接世界，分享精彩') }}</text>
    </view>

    <!-- Login Form -->
    <view class="form-container">
      <view class="login-type-switch">
        <view class="switch-item" :class="{ active: loginType === 'phone' }" @click="switchLoginType('phone')">
          <text>{{ $t('手机号登录') }}</text>
        </view>
        <view class="switch-item" :class="{ active: loginType === 'email' }" @click="switchLoginType('email')">
          <text>{{ $t('邮箱登录') }}</text>
        </view>
        <view class="switch-item" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">
          <text>{{ $t('密码登录') }}</text>
        </view>
      </view>

      <!-- 验证码登录表单 -->
      <block v-if="loginType === 'phone' || loginType === 'email'">
        <view class="form-group">
          <view class="input-wrapper">
            <text class="input-icon">{{ loginType === 'phone' ? '📱' : '📧' }}</text>
            <input class="input" :type="loginType === 'phone' ? 'number' : 'text'" v-model="loginForm.account"
                   :placeholder="loginType === 'phone' ? $t('请输入手机号') : $t('请输入邮箱')" @input="clearError"/>
          </view>
        </view>

        <view class="form-group code-group">
          <view class="input-wrapper code-wrapper">
            <text class="input-icon">🔢</text>
            <input class="input" type="number" v-model="loginForm.code" :placeholder="$t('验证码')" maxlength="4"
                   @input="clearError"/>
          </view>
          <view class="get-code-button" :class="{ 'disabled': countdown > 0 || !isValidAccount }" @click="sendCode">
            <text>{{ countdown > 0 ? `${countdown}s` : $t('获取验证码') }}</text>
          </view>
        </view>
      </block>

      <!-- 密码登录表单 -->
      <block v-if="loginType === 'password'">
        <view class="form-group">
          <view class="input-wrapper">
            <text class="input-icon">👤</text>
            <input class="input" type="text" v-model="passwordForm.username" :placeholder="$t('请输入账号')"
                   @input="clearError"/>
          </view>
        </view>

        <view class="form-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input class="input" :type="showPassword ? 'text' : 'password'" v-model="passwordForm.password"
                   :placeholder="$t('请输入密码')" @input="clearError"/>
            <text class="eye-icon" @click="togglePasswordVisibility">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
          </view>
        </view>

        <!-- 确认密码输入框，仅在注册模式显示 -->
        <view class="form-group" v-if="isRegistering">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input class="input" :type="showPassword ? 'text' : 'password'" v-model="passwordForm.password2"
                   :placeholder="$t('请确认密码')" @input="clearError"/>
          </view>
        </view>

        <view class="helper-links">
          <text class="forgot-password"></text>
          <!-- <text class="forgot-password">忘记密码?</text> -->
          <text class="switch-tab" @click="isRegistering = !isRegistering">{{
              isRegistering ? $t('返回登录') : $t('去注册')
            }}
          </text>
        </view>
      </block>

      <view class="error-message" v-if="error">
        <text>{{ error }}</text>
      </view>

      <view class="action-button" @click="handleAction">
        <text>{{ isRegistering ? $t('注册') : $t('登录') }}</text>
      </view>
    </view>

    <!-- Third-party login options -->
    <view class="third-party-login">
      <view class="divider">
        <view class="line"></view>
        <text class="text">{{ $t('社交账号登录') }}</text>
        <view class="line"></view>
      </view>

      <view class="third-party-buttons">
        <view class="third-party-button" @click="loginWithThirdParty('wechat')">
          <image class="third-party-icon" src="/static/icons/wechat.png" mode="aspectFit"></image>
        </view>
        <view class="third-party-button" @click="loginWithThirdParty('qq')">
          <image class="third-party-icon" src="/static/icons/qq.png" mode="aspectFit"></image>
        </view>
        <view class="third-party-button" @click="loginWithThirdParty('weibo')">
          <image class="third-party-icon" src="/static/icons/weibo.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- Guest login button -->
    <view class="guest-login">
      <text class="guest-login-text" @click="loginAsGuest">{{ $t('游客访问') }}</text>
    </view>
  </view>
</template>

<script>
import {AppCommonCtrlEmailSendCode} from "../../network/common/email";
import {
  AppCommonCtrlUserEmailLogin,
  AppCommonCtrlUserPhoneLogin,
  AppCommonCtrlUserPwdLogin,
  AppCommonCtrlUserPwdRegister
} from "../../network/common/user";
import {AppCommonCtrlSmsApiSend} from "../../network/common/sms-api";
import {User} from "../../Lib/User";
import {AppsLiveApiLiveUserApiDetail} from "../../network/api/live-user";
import i18n from "@/Lib/i18n";

export default {
  data() {
    return {
      loginType: 'phone', // 'phone' or 'email' or 'password'
      loginForm: {
        account: '',
        code: ''
      },
      passwordForm: {
        username: '',
        password: '',
        password2: ''
      },
      error: '',
      countdown: 0,
      timer: null,
      showPassword: false,
      isRegistering: false,
      retUrl: '',
    }
  },
  onShow() {
    i18n.updatePageTitle('登录')
  },
  onLoad(opt) {
    if (opt.retUrl) {
      this.retUrl = opt.retUrl
    }
  },
  computed: {
    isValidAccount() {
      if (this.loginType === 'phone') {
        const phoneRegex = /^1[3-9]\d{9}$/
        return phoneRegex.test(this.loginForm.account)
      } else if (this.loginType === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(this.loginForm.account)
      }
      return true
    },
    isValidPasswordForm() {
      if (this.isRegistering) {
        return this.passwordForm.username &&
            this.passwordForm.password &&
            this.passwordForm.password.length >= 6 &&
            this.passwordForm.password === this.passwordForm.password2
      }
      return this.passwordForm.username && this.passwordForm.password && this.passwordForm.password.length >= 6
    }
  },
  methods: {
    switchLoginType(type) {
      this.loginType = type
      this.loginForm.account = ''
      this.loginForm.code = ''
      this.passwordForm.username = ''
      this.passwordForm.password = ''
      this.passwordForm.password2 = ''
      this.isRegistering = false
      this.clearError()
    },
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    },
    clearError() {
      this.error = ''
    },
    async sendCode() {
      if (!this.isValidAccount) {
        this.error = this.loginType === 'phone' ? this.$t('请输入有效的手机号码') : this.$t('请输入有效的邮箱地址')
        return
      }

      if (this.countdown > 0) return

      uni.showLoading({
        title: this.$t('发送中...')
      })

      try {
        if (this.loginType === 'phone') {
          await AppCommonCtrlSmsApiSend({
            phone: this.loginForm.account
          })
        } else {
          await AppCommonCtrlEmailSendCode({
            email: this.loginForm.account
          })
        }

        uni.hideLoading()
        uni.showToast({
          title: this.$t('验证码已发送'),
          icon: 'success',
          duration: 2000
        })

        this.countdown = 60
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
          }
        }, 1000)
      } catch (err) {
        uni.hideLoading()
        this.error = err.message || this.$t('发送验证码失败，请重试')
      }
    },
    handleAction() {
      if (this.isRegistering) {
        this.register()
      } else {
        this.login()
      }
    },
    async register() {
      if (!this.passwordForm.username || !this.passwordForm.password) {
        this.error = this.$t('请输入账号和密码')
        return
      }

      if (this.passwordForm.password.length < 6) {
        this.error = this.$t('密码长度至少为6位')
        return
      }

      if (this.passwordForm.password !== this.passwordForm.password2) {
        this.error = this.$t('两次输入的密码不一致')
        return
      }

      uni.showLoading({
        title: this.$t('注册中...')
      })

      try {
        await AppCommonCtrlUserPwdRegister({
          username: this.passwordForm.username,
          password: this.passwordForm.password,
          password2: this.passwordForm.password2
        })

        uni.hideLoading()
        uni.showToast({
          title: this.$t('注册成功，请登录'),
          icon: 'success',
          duration: 1500
        })

        this.isRegistering = false
      } catch (err) {
        uni.hideLoading()
        this.error = err.message || this.$t('注册失败，请重试')
      }
    },
    async login() {
      if (this.loginType === 'password') {
        if (!this.isValidPasswordForm) {
          this.error = this.$t('请输入有效的账号和密码')
          return
        }

        uni.showLoading({
          title: this.$t('登录中...')
        })

        try {
          const loginResult = await AppCommonCtrlUserPwdLogin({
            username: this.passwordForm.username,
            password: this.passwordForm.password
          })

          uni.hideLoading()
          this.handleLoginSuccess(loginResult)
        } catch (err) {
          uni.hideLoading()
          this.error = err.message || this.$t('登录失败，请重试')
        }
      } else {
        if (!this.isValidAccount) {
          this.error = this.loginType === 'phone' ? this.$t('请输入有效的手机号码') : this.$t('请输入有效的邮箱地址')
          return
        }

        if (!this.loginForm.code || this.loginForm.code.length !== 4) {
          this.error = this.$t('请输入4位验证码')
          return
        }

        uni.showLoading({
          title: this.$t('登录中...')
        })

        try {
          let loginResult
          if (this.loginType === 'phone') {
            loginResult = await AppCommonCtrlUserPhoneLogin({
              phone: this.loginForm.account,
              code: this.loginForm.code
            })
          } else {
            loginResult = await AppCommonCtrlUserEmailLogin({
              email: this.loginForm.account,
              code: this.loginForm.code
            })
          }

          uni.hideLoading()
          this.handleLoginSuccess(loginResult)
        } catch (err) {
          uni.hideLoading()
          this.error = err.message || this.$t('登录失败，请重试')
        }
      }
    },
    handleLoginSuccess(loginResult) {
      console.log('loginResult:')
      console.log(loginResult)
      uni.showToast({
        title: this.$t('登录成功'),
        icon: 'success',
        duration: 1500
      })

      // 存储用户信息和token
      User.setLongToken(loginResult.token)
      User.setUserId(loginResult.id)
      uni.$emit('updateUserCoins')
      // 检查用户是否填写了个人信息
      this.checkUserProfile(loginResult.id)
    },
    async checkUserProfile(userId) {
      console.log(userId)
      try {
        const userDetail = await AppsLiveApiLiveUserApiDetail()

        if (!userDetail.nickname) {
          // 如果没有昵称，跳转到个人信息编辑页面
          uni.redirectTo({
            url: '/pages/profile/edit'
          })
        } else {
          console.log("login2")

          // 有昵称，检查页面栈后返回或跳转首页
          const pages = getCurrentPages()
          if (pages.length <= 1) {
            // 没有页面栈，跳转到首页
            uni.switchTab({
              url: '/pages/index/index'
            })
          } else {
            // 有页面栈，返回上一页
            uni.navigateBack()
          }
        }
      } catch (error) {
        console.error(this.$t('获取用户信息失败'), error)
        // 获取用户信息失败时，检查页面栈后返回或跳转首页
        const pages = getCurrentPages()
        if (pages.length <= 1) {
          // 没有页面栈，跳转到首页
          uni.switchTab({
            url: '/pages/index/index'
          })
        } else {
          // 有页面栈，返回上一页
          uni.navigateBack()
        }
      }
    },
    loginWithThirdParty(platform) {
      uni.showToast({
        title: this.$t('正在使用%s登录', [platform]),
        icon: 'none',
        duration: 2000
      })
    },
    loginAsGuest() {
      uni.showLoading({
        title: this.$t('正在进入...')
      })

      uni.hideLoading()
      uni.setStorageSync('isGuest', true)
      uni.setStorageSync('user', {id: 'guest', name: this.$t('游客'), avatar: '/static/avatar/guest.png'})

      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style>
.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
  padding: 40rpx;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60rpx 0;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.slogan {
  font-size: 28rpx;
  color: #999;
}

.form-container {
  margin-top: 40rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  height: 100rpx;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
}

.input-icon {
  margin-right: 20rpx;
  font-size: 32rpx;
}

.input {
  flex: 1;
  height: 100rpx;
  font-size: 28rpx;
}

.eye-icon {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
}

.code-group {
  display: flex;
  justify-content: space-between;
}

.code-wrapper {
  flex: 1;
  margin-right: 20rpx;
}

.get-code-button {
  width: 200rpx;
  height: 100rpx;
  background-color: #FF5A5F;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.get-code-button.disabled {
  background-color: #ccc;
}

.get-code-button text {
  color: white;
  font-size: 24rpx;
}

.helper-links {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.forgot-password,
.switch-tab {
  font-size: 26rpx;
  color: #666;
}

.switch-tab {
  color: #FF5A5F;
}

.error-message {
  color: #FF5A5F;
  font-size: 24rpx;
  margin-bottom: 20rpx;
  padding-left: 30rpx;
}

.action-button {
  height: 100rpx;
  background-color: #FF5A5F;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

.action-button.disabled {
  background-color: #ccc;
}

.action-button text {
  color: white;
  font-size: 32rpx;
}

.agreement {
  margin-bottom: 30rpx;
}

.agreement-label {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.agreement-link {
  font-size: 24rpx;
  color: #FF5A5F;
}

.third-party-login {
  margin-top: 60rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.line {
  flex: 1;
  height: 1rpx;
  background-color: #e0e0e0;
}

.divider .text {
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #999;
}

.third-party-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.third-party-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 30rpx;
}

.third-party-icon {
  width: 40rpx;
  height: 40rpx;
}

.guest-login {
  display: flex;
  justify-content: center;
}

.guest-login-text {
  font-size: 26rpx;
  color: #999;
  padding: 20rpx;
}

.login-type-switch {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.switch-item {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  display: flex;
  align-items: flex-end;
  text-align: center;
  justify-content: center;
  flex: 1;
}

.switch-item.active {
  color: #FF5A5F;
  font-weight: bold;
}

.switch-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #FF5A5F;
  border-radius: 2rpx;
}
</style>