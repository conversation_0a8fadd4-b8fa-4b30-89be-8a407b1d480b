<template>
  <network-monitor>
    <view class="video-categories">

      <header-section @search="performSearch"></header-section>
      <view style="height: 20rpx;background: #ffffff;"></view>

      <!-- Banner -->
      <banner-swiper position-id="video"></banner-swiper>

            <!-- Latest Videos -->
       <view class="latest-videos pt-lg">
        <text class="section-title">{{ $t('最新上传') }}</text>
        <scroll-view class="categories-scroll" scroll-x>
          <view
              class="category-item mt-lg"
              v-for="video in latestVideos"
              :key="video.id"
              @click="goToVideo(video.id)"
          >
            <image :src="video.coverUrl" mode="aspectFill"></image>
            <text class="category-name">{{ video.title }}</text>
            <text class="category-count">{{ formatNumber(video.viewNum) }}{{ $t('次观看') }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- Popular Videos -->
      <view class="popular-videos">
        <view class="section-header">
          <text class="section-title">{{ $t('热门推荐') }}</text>
          <view class="more-link flex-row align-center" @click="goToCategory(0)">
            <text>{{ $t('查看更多') }}</text>
            <uni-icons type="right" size="14" color="#1da1f2"></uni-icons>
          </view>
        </view>

        <view class="video-grid">
          <view
              class="video-item"
              v-for="video in popularVideos"
              :key="video.id"
              @click="goToVideo(video.id)"
          >
            <view class="video-cover">
              <image class="video" mode="aspectFill" :src="video.coverUrl"></image>
              <view class="duration">{{ formatDuration(video.duration) }}</view>
              <!-- 直播状态标识 -->
              <view v-if="video.user && video.user.isLive" class="live-badge" @click.stop="enterLiveRoom(video.user.userId)">
                <text>{{ $t('直播中') }}</text>
              </view>
              <!--              <view v-if="video.paymentType !== 'free'" class="payment-badge">-->
              <!--                <uni-icons type="locked" size="14" color="#fff"></uni-icons>-->
              <!--                <text>{{ video.paymentType === 'vip' ? 'VIP' : $t('付费') }}</text>-->
              <!--              </view>-->
            </view>
            <view class="video-info">
              <text class="video-title">{{ video.title }}</text>
              <view class="video-stats">
                <view class="flex-row align-center">
                  <uni-icons type="eye" size="12" color="#999"></uni-icons>
                  <text class="ml-xs">{{ formatNumber(video.viewNum) }}</text>
                </view>
                <view class="flex-row align-center">
                  <uni-icons type="calendar" size="12" color="#999"></uni-icons>
                  <text class="ml-xs">{{ formatTimeAgo(video.createdAt) }}</text>
                </view>
              </view>
              <view class="video-user">
                <image :src="video.user.head" mode="aspectFill" class="user-avatar"
                       @click.stop="goToUser(video.user.userId)"></image>
                <text @click.stop="goToUser(video.user.userId)">{{ video.user.nickname }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>



      <!-- 发布视频悬浮按钮 -->
      <view class="floating-button" @click="$loginNavigateTo('/pages/video/upload')">
        <uni-icons class="icon" type="plusempty" size="20" color="#FFFFFF"></uni-icons>
      </view>

      <!-- 上拉加载提示 -->
      <view class="pull-up-tip" v-if="isPulling">
        <text>{{ $t('继续上拉查看更多') }}</text>
        <uni-icons type="arrow-up" size="16" color="#999"></uni-icons>
      </view>

    </view>
  </network-monitor>
</template>

<script>
import BannerSwiper from '../../components/BannerSwiper.vue';
import HeaderSection from '../../components/HeaderSection.vue';
import {AppsLiveApiLiveVideosApiLists} from '@/network/api/live-videos';
import {AppsLiveApiLiveRoomsApiDetailByUserId} from '@/network/api/live-rooms';
import NetworkMonitor from "@/components/NetworkMonitor.vue";
import i18n from "@/Lib/i18n";

export default {
  components: {
    NetworkMonitor,
    BannerSwiper,
    HeaderSection
  },

  data() {
    return {
      popularVideos: [],
      latestVideos: [],
      searchKeyword: '',
      showMoreLatest: false,
      isPulling: false,
      pullStartY: 0,
      pullDistance: 0,
    }
  },
  onShow() {
    uni.$emit('updateUserCoins')
    i18n.updatePageTitle("视频")
    this.loadData();
  },
  onLoad() {

  },

  onPageScroll(e) {

  },

  onReachBottom() {
    // 当页面滚动到底部时，切换到视频列表页面
    if (!this.isPulling) {
      this.isPulling = true;
      setTimeout(() => {
        this.goToCategory(0);
      }, 500);
    }
  },

  methods: {
    performSearch(text) {
      console.log(text)
      this.searchKeyword = text
      uni.navigateTo({
        url: `/pages/video/list?categoryId=0&title=${text}`
      });
    },
    async loadData() {
      try {
        // Get popular videos (sorted by likes)
        const popularResponse = await AppsLiveApiLiveVideosApiLists({
          ext: {
            sortField: 'LIKES',
            sortOrder: 'DESC',
            page: 1,
            pageSize: 4
          }
        });
        this.popularVideos = popularResponse.lists || [];

        // Get latest videos (sorted by id)
        const latestResponse = await AppsLiveApiLiveVideosApiLists({
          ext: {
            sortField: 'id',
            sortOrder: 'DESC',
            page: 1,
            pageSize: 10
          }
        });
        console.log(latestResponse)
        this.latestVideos = latestResponse.lists || [];
      } catch (error) {
        console.error('Failed to load data:', error);
        uni.showToast({
          title: $t('加载数据失败'),
          icon: 'none'
        });
      }
    },

    goToCategory(categoryId) {
      uni.navigateTo({
        url: `/pages/video/list?categoryId=${categoryId}`
      });
    },

    goToVideo(videoId) {
      uni.navigateTo({
        url: `/pages/video/detail?id=${videoId}`
      });
    },

    goToUser(userId) {
      uni.navigateTo({
        url: `/pages/user/index?id=${userId}`
      });
    },


    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      }
      return num.toString();
    },

    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    formatTimeAgo(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
        if (diffHours === 0) {
          const diffMinutes = Math.floor(diffTime / (1000 * 60));
          return this.$t(`%s分钟前`, diffMinutes);
        }
        return this.$t(`%s小时前`, diffHours);
      } else if (diffDays < 30) {
        return this.$t(`%s天前`, diffDays);
      } else {
        // Format as MM-DD
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${month}-${day}`;
      }
    },

    truncateDesc(text, maxLength = 50) {
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },

    // 进入直播间
    async enterLiveRoom(userId) {
      try {
        // 调用获取直播间详情接口
        const roomData = await AppsLiveApiLiveRoomsApiDetailByUserId({
          userId: userId
        });
        
        if (roomData && roomData.id) {
          // 跳转到直播间页面
          uni.navigateTo({
            url: `/pages/live/room?roomId=${roomData.id}`
          });
        } else {
          uni.showToast({
            title: this.$t('直播间信息获取失败'),
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取直播间信息失败', error);
        uni.showToast({
          title: this.$t('进入直播间失败'),
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.video-categories {
  padding-bottom: 60px;
}

.section-title {
  font-size: $uni-font-size-xl;
  font-weight: bold;
  margin: $uni-spacing-col-lg 0;
  padding: 0 $uni-spacing-row-lg;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 $uni-spacing-row-lg;
  margin: $uni-spacing-col-lg 0;
}

.more-link {
  font-size: $uni-font-size-base;
  color: $app-secondary-color;
}

.categories-scroll {
  white-space: nowrap;
  padding: 0 $uni-spacing-row-lg;
}

.category-item {
  display: inline-block;
  margin-right: $uni-spacing-row-lg;
  width: 240rpx;
  height: 200rpx;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  position: relative;
}

.category-item image {
  width: 100%;
  height: 100%;
}

.category-name {
  position: absolute;
  bottom: 50rpx;
  left: $uni-spacing-row-base;
  color: $uni-text-color-inverse;
  font-size: $uni-font-size-lg;
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.category-count {
  position: absolute;
  bottom: $uni-spacing-col-base;
  left: $uni-spacing-row-base;
  color: rgba(255, 255, 255, 0.8);
  font-size: $uni-font-size-sm;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.video-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 $uni-spacing-row-base;
}

.video-item {
  width: calc(50% - 20rpx);
  margin: $uni-spacing-col-xs;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  background-color: $uni-bg-color;
  box-shadow: $uni-shadow-sm;
}

.video-cover {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.video-cover .video {
  width: 100%;
  height: 100%;
}

.duration {
  position: absolute;
  right: $uni-spacing-row-xs;
  bottom: $uni-spacing-col-xs;
  background-color: rgba(0, 0, 0, 0.7);
  color: $uni-text-color-inverse;
  padding: 4rpx 10rpx;
  border-radius: $uni-border-radius-sm;
  font-size: $uni-font-size-sm;
}

.payment-badge {
  position: absolute;
  left: $uni-spacing-row-xs;
  top: $uni-spacing-col-xs;
  background-color: $app-primary-color;
  color: $uni-text-color-inverse;
  padding: 4rpx 12rpx;
  border-radius: $uni-border-radius-sm;
  font-size: $uni-font-size-sm;
  display: flex;
  align-items: center;
}

.payment-badge text {
  margin-left: $uni-spacing-row-xs;
}

.live-badge {
  position: absolute;
  left: $uni-spacing-row-xs;
  top: $uni-spacing-col-xs;
  background-color: #FF5A5F;
  color: $uni-text-color-inverse;
  padding: 4rpx 12rpx;
  border-radius: $uni-border-radius-sm;
  font-size: $uni-font-size-sm;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s;
}

.live-badge:hover {
  opacity: 0.8;
}

.live-badge text {
  font-weight: 500;
}

.video-info {
  padding: $uni-spacing-col-base;
}

.video-title {
  font-size: $uni-font-size-base;
  font-weight: 500;
  margin-bottom: $uni-spacing-col-xs;
  line-height: 1.3;
  @extend .text-ellipsis-2;
}

.video-stats {
  display: flex;
  justify-content: space-between;
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
  margin-bottom: $uni-spacing-col-xs;
}

.video-user {
  display: flex;
  align-items: center;
  font-size: $uni-font-size-sm;
  color: $uni-text-color;
}

.user-avatar {
  width: $uni-img-size-sm;
  height: $uni-img-size-sm;
  border-radius: $uni-border-radius-circle;
  margin-right: $uni-spacing-row-xs;
  cursor: pointer;
}

.video-user text {
  cursor: pointer;
  color: $uni-text-color;
}

.video-user text:hover {
  color: $app-primary-color;
}

.video-list {
  padding: 0 $uni-spacing-row-lg;
}

.video-list-item {
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-lg;
  background-color: $uni-bg-color;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  box-shadow: $uni-shadow-sm;
}

.video-thumb {
  width: 240rpx;
  height: 140rpx;
  border-top-left-radius: $uni-border-radius-base;
  border-bottom-left-radius: $uni-border-radius-base;
  flex-shrink: 0;
}

.video-desc {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
  margin-top: $uni-spacing-col-xs;
  line-height: 1.3;
  @extend .text-ellipsis;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
  margin-top: $uni-spacing-col-xs;
}

.video-tags {
  margin-top: $uni-spacing-col-xs;
}

.tag {
  background-color: $uni-bg-color-tag;
  color: $app-secondary-color;
  padding: 4rpx 12rpx;
  border-radius: $uni-border-radius-base;
  margin-right: $uni-spacing-row-xs;
  font-size: $uni-font-size-sm;
}

// 悬浮按钮
.floating-button {
  position: fixed;
  right: $uni-spacing-row-xl;
  bottom: 160rpx;
  background: linear-gradient(to right, $app-primary-color, darken($app-primary-color, 10%));
  color: $uni-text-color-inverse;
  height: 80rpx;
  width: 80rpx;
  border-radius: $uni-border-radius-pill;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $uni-shadow-brand;
  z-index: 9999;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  text {
    font-size: $uni-font-size-base;
    font-weight: 500;
  }
}

.pull-up-tip {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: transform 0.3s ease;

  text {
    font-size: $uni-font-size-sm;
    color: $uni-text-color-grey;
    margin-right: $uni-spacing-row-xs;
  }
}


</style>

