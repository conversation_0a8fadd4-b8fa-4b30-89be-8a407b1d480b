<template>
  <view class="social-post" @click="navigateToDetail">
    <!-- Layout for single image or no image -->
    <template v-if="imageContentItems.length <= 1">
      <view class="single-image-layout" :class="{ 'no-image': imageContentItems.length === 0 }">
        <!-- Left: Single Image -->
        <view v-if="imageContentItems.length === 1" class="post-media-left">
          <ContentUnlocking
              class="single-post-image"
              :payment-type="imageContentItems[0].paymentType"
              :price="imageContentItems[0].price"
              :member-ids="imageContentItems[0].memberIds"
              target-table="live_post_content"
              :target-id="imageContentItems[0].id"
              :image="imageContentItems[0].mediaContent"
              :blur-image="!!imageContentItems[0].mediaContent"
          >
            <image
                class="image"
                :src="imageContentItems[0].mediaContent"
                mode="aspectFill"
                @click.stop="previewImages(0)"
            ></image>
          </ContentUnlocking>
        </view>

        <!-- Right: Content -->
        <view class="post-content-right">
          <!-- Post Title -->
          <view class="post-title" v-if="post.title">
            <text>{{ post.title }}</text>
          </view>

          <!-- Bottom Section: User Info and Stats -->
          <view class="bottom-section">
            <!-- User Info -->
            <view class="post-user-info-bottom" @click.stop="navigateToUser">
              <image class="user-avatar-small" :src="userAvatar" mode="aspectFill"/>
              <view class="user-info-content">
                <view class="username-row">
                  <!-- 直播标识 -->
                  <view v-if="userIsLive" class="live-badge live-badge-small">
                    <uni-icons type="videocam-filled" size="10" color="#FFFFFF"></uni-icons>
                    <text class="live-text">{{ $t('直播中') }}</text>
                  </view>
                  <text class="post-username-small">{{ userNickname }}</text>
                </view>
                <text class="post-time-small">{{ formatTime(post.createdAt) }}</text>
              </view>
            </view>

            <!-- Stats -->
            <view class="post-stats">
              <view class="stat-item" :class="{ 'active': post.hasCollect }" @click.stop="toggleLike">
                <uni-icons :type="post.hasCollect ? 'heart-filled' : 'heart'" size="16"
                           :color="post.hasCollect ? '#FF5A5F' : '#999999'"></uni-icons>
                <text>{{ formatNumber(post.collectNum) }}</text>
              </view>
              <view class="stat-item" @click.stop="navigateToComments">
                <uni-icons type="chatbubble" size="16" color="#999999"></uni-icons>
                <text>{{ formatNumber(post.commentNum) }}</text>
              </view>
              <view class="stat-item" @click.stop="sharePost">
                <uni-icons type="redo" size="16" color="#999999"></uni-icons>
                <text>{{ formatNumber(post.shareNum) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- Layout for multiple images (2-3 images) -->
    <template v-else>
      <view class="multiple-images-layout">
        <!-- Post Header -->
        <view class="post-header">
          <image class="post-avatar" :src="userAvatar" mode="aspectFill"
                 @click.stop="navigateToUser"/>
          <view class="post-user-info" @click.stop="navigateToUser">
            <view class="username-with-live">
              <!-- 直播标识 -->
              <view v-if="userIsLive" class="live-badge live-badge-large">
                <uni-icons type="videocam-filled" size="12" color="#FFFFFF"></uni-icons>
                <text class="live-text">{{ $t('直播中') }}</text>
              </view>
              <text class="post-username">{{ userNickname }}</text>
            </view>
            <text class="post-time">{{ formatTime(post.createdAt) }}</text>
          </view>

          <!-- Stats in header for multiple images -->
          <view class="header-stats">
            <view class="stat-item" :class="{ 'active': post.hasCollect }" @click.stop="toggleLike">
              <uni-icons :type="post.hasCollect ? 'heart-filled' : 'heart'" size="14"
                         :color="post.hasCollect ? '#FF5A5F' : '#999999'"></uni-icons>
              <text>{{ formatNumber(post.collectNum) }}</text>
            </view>
            <view class="stat-item" @click.stop="navigateToComments">
              <uni-icons type="chatbubble" size="14" color="#999999"></uni-icons>
              <text>{{ formatNumber(post.commentNum) }}</text>
            </view>
            <view class="stat-item" @click.stop="sharePost">
              <uni-icons type="redo" size="14" color="#999999"></uni-icons>
              <text>{{ formatNumber(post.shareNum) }}</text>
            </view>
          </view>

          <view class="post-actions" @click.stop>
            <uni-icons type="more-filled" size="20" color="#999999" @click="showActionsSheet"></uni-icons>
          </view>
        </view>

        <!-- Post Content -->
        <view class="post-content">
          <!-- Post Title -->
          <view class="post-title" v-if="post.title">
            <text>{{ post.title }}</text>
          </view>

          <!-- Multiple Images -->
          <view class="post-media">
            <view class="images-container">
              <template v-for="(imageItem, index) in imageContentItems" :key="imageItem.id">
                <ContentUnlocking
                    class="post-image"
                    :class="'image-count-' + imageContentItems.length"
                    :payment-type="imageItem.paymentType"
                    :price="imageItem.price"
                    :member-ids="imageItem.memberIds"
                    target-table="live_post_content"
                    :target-id="imageItem.id"
                    :image="imageItem.mediaContent"
                    :blur-image="!!imageItem.mediaContent"
                >
                  <image
                      class="image"
                      :src="imageItem.mediaContent"
                      mode="aspectFit"
                      @click.stop="previewImages(index)"
                  ></image>
                </ContentUnlocking>
              </template>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- Comments Section (Optional) -->
    <view class="comments-section" v-if="post.comments && post.comments.length > 0">
      <view class="section-divider"></view>
      <view class="comment-item" v-for="(comment) in post.comments" :key="comment.id" @click.stop="navigateToComments">
        <view class="comment-content">
          <view class="comment-user-info" @click.stop="navigateToUserFromComment(comment)">
            <image class="comment-avatar" :src="getCommentUserAvatar(comment)" mode="aspectFill"/>
            <view class="comment-user-content">
              <!-- 评论用户直播标识 -->
              <view v-if="getCommentUserIsLive(comment)" class="live-badge live-badge-comment">
                <uni-icons type="videocam-filled" size="10" color="#FFFFFF"></uni-icons>
                <text class="live-text">{{ $t('直播中') }}</text>
              </view>
              <text class="comment-username">{{ getCommentUserNickname(comment) }}</text>
            </view>
          </view>
          <text class="comment-text">{{ comment.mediaContent }}</text>
        </view>
      </view>
      <view class="more-comments" v-if="post && post.commentNum > post.comments.length"
            @click.stop="navigateToComments">
        {{ $t('查看全部') }} {{ post.commentNum }} {{ $t('条评论') }}
      </view>
    </view>
  </view>
</template>

<script>
import {AppsLiveApiLivePostCollectApiToggle} from '@/network/api/live-post-collect';
import {AppsLiveApiLivePostDislikeApiSave} from '@/network/api/live-post-dislike';
import {User} from "@/Lib/User";
import ContentUnlocking from './ContentUnlocking.vue';

export default {
  name: 'SocialPost',

  props: {
    post: {
      type: Object,
      required: true
    },
  },

  components: {
    ContentUnlocking
  },

  data() {
    return {}
  },

  watch: {
    post: {
      handler(newPost) {
        // No need to watch isLiked anymore as we use post.hasCollect directly
      },
      immediate: true
    }
  },
  created() {
    setTimeout(()=>{
      console.log( this.userNickname + ' - '  + this.userIsLive)
    },2500);
  },
  computed: {
    // 安全获取用户头像
    userAvatar() {
      return this.post && this.post.user && this.post.user.head ? this.post.user.head : '/static/icons/usr.png';
    },

    // 安全获取用户昵称
    userNickname() {
      return this.post && this.post.user && this.post.user.nickname ? this.post.user.nickname : '';
    },

    // 安全获取用户ID
    userId() {
      return this.post && this.post.user && this.post.user.userId ? this.post.user.userId : null;
    },

    // 安全获取用户直播状态
    userIsLive() {
      return this.post && this.post.user && this.post.user.isLive;
    },

    // 获取图片内容项（最多3张）
    imageContentItems() {
      if (!this.post.content || !Array.isArray(this.post.content)) return [];
      return this.post.content
          .filter(item => item.mediaType === 1) // IMAGE
          .slice(0, 3); // 最多3张图片
    },

    // 用于获取所有图片URL（用于预览）
    imageUrls() {
      return this.imageContentItems.map(item => item.mediaContent);
    }
  },

  methods: {
    // 安全获取评论用户头像
    getCommentUserAvatar(comment) {
      return comment && comment.user && comment.user.head ? comment.user.head : '/static/icons/usr.png';
    },

    // 安全获取评论用户昵称
    getCommentUserNickname(comment) {
      return comment && comment.user && comment.user.nickname ? comment.user.nickname : '';
    },

    // 安全获取评论用户ID
    getCommentUserId(comment) {
      return comment && comment.user && comment.user.userId ? comment.user.userId : null;
    },

    // 安全获取评论用户直播状态
    getCommentUserIsLive(comment) {
      return comment && comment.user && comment.user.isLive;
    },

    formatTime(dateString) {
      const now = new Date();
      const date = new Date(dateString);
      const diff = Math.floor((now.getTime() - date.getTime()) / 1000); // seconds

      if (diff < 60) {
        return this.$t('刚刚');
      } else if (diff < 3600) {
        return this.$t(`%s分钟前`, Math.floor(diff / 60));
      } else if (diff < 86400) {
        return this.$t("%s小时前", Math.floor(diff / 3600));
      } else if (diff < 604800) { // Within a week
        return this.$t(`%s天前`, Math.floor(diff / 86400));
        // return `${Math.floor(diff / 86400)}天前`;
      } else {
        return `${date.getMonth() + 1}-${date.getDate()}`;
      }
    },

    formatNumber(num) {
      if (num === undefined || num === null) return '0';
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
      }
      return num.toString();
    },

    navigateToUser() {
      if (this.userId) {
        uni.navigateTo({
          url: `/pages/user/index?id=${this.userId}`
        });
      }
    },

    navigateToUserFromComment(comment) {
      const userId = this.getCommentUserId(comment);
      if (userId) {
        uni.navigateTo({
          url: `/pages/user/index?id=${userId}`
        });
      }
    },

    toggleLike() {

      AppsLiveApiLivePostCollectApiToggle({
        postId: this.post.id,
        userId: User.getUserId()
      })
          .then(response => {
            if (response.success) {
              this.post.hasCollect = !this.post.hasCollect;
              // Update the post collectNum count
              if (this.post.hasCollect) {
                this.post.collectNum = (this.post.collectNum || 0) + 1;
              } else {
                this.post.collectNum = Math.max(0, (this.post.collectNum || 0) - 1);
              }

              uni.showToast({
                title: this.post.hasCollect ? this.$t('已收藏') : this.$t('已取消收藏'),
                icon: 'success'
              });
            } else {
              uni.showToast({
                title: this.$t('操作失败'),
                icon: 'none'
              });
            }
          })
          .catch(error => {
            console.error('Like toggle error:', error);
            uni.showToast({
              title: this.$t('网络异常，请稍后再试'),
              icon: 'none'
            });
          });
    },

    navigateToDetail() {
      uni.navigateTo({
        url: `/pages/social/detail?id=${this.post.id}`
      });
    },

    navigateToComments() {
      uni.navigateTo({
        url: `/pages/social/detail?id=${this.post.id}&tab=comments`
      });
    },

    previewImages(current) {
      if (!this.imageUrls || this.imageUrls.length === 0) return;

      uni.previewImage({
        urls: this.imageUrls,
        current: current,
      });
    },

    sharePost() {
      uni.showActionSheet({
        itemList: [this.$t('分享给朋友'), this.$t('分享到朋友圈'), this.$t('复制链接')],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },

    showActionsSheet() {
      uni.showActionSheet({
        itemList: [this.$t('收藏'), this.$t('不感兴趣'), this.$t('举报')],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 点击收藏
            this.toggleLike();
          } else if (res.tapIndex === 1) {
            // 不感兴趣
            // 显示加载提示
            uni.showLoading({
              title: this.$t('处理中...')
            });

            // 调用不感兴趣接口
            AppsLiveApiLivePostDislikeApiSave({
              postId: this.post.id,
              userId: User.getUserId()
            })
                .then(response => {
                  uni.hideLoading();

                  if (response.success) {
                    uni.showToast({
                      title: this.$t('已设置不感兴趣，将减少此类内容推送'),
                      icon: 'none',
                      duration: 2000
                    });
                  } else {
                    uni.showToast({
                      title: this.$t('操作失败'),
                      icon: 'none'
                    });
                  }
                })
                .catch(error => {
                  uni.hideLoading();
                  console.error('设置不感兴趣失败:', error);
                  uni.showToast({
                    title: this.$t('网络异常，请稍后再试'),
                    icon: 'none'
                  });
                });
          } else if (res.tapIndex === 2) {
            uni.navigateTo({
              url: '/pages/feedback/report?type=post&id=' + this.post.id
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
// Using uni.scss variables

.social-post {
  background-color: $uni-bg-color;
  padding: $uni-spacing-col-lg;
  margin-bottom: $uni-spacing-col-base;
  border-radius: $uni-border-radius-lg;
  box-shadow: $uni-shadow-sm;

  // Single image layout (left image + right content)
  .single-image-layout {
    display: flex;
    align-items: flex-start;
    gap: $uni-spacing-row-base;

    &.no-image {
      .post-content-right {
        height: auto;
        min-height: 120rpx;
      }
    }

    .post-media-left {
      flex-shrink: 0;

      .single-post-image {
        width: 200rpx;
        height: 200rpx;
        border-radius: $uni-border-radius-sm;
        overflow: hidden;

        .image {
          width: 100%;
          height: 100%;
          border-radius: $uni-border-radius-sm;
        }
      }
    }

    .post-content-right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 200rpx;

      .post-title {
        font-size: $uni-font-size-lg;
        color: $uni-text-color;
        font-weight: 500;
        line-height: 1.4;
        margin-bottom: $uni-spacing-col-sm;
        flex: 1;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .bottom-section {
        margin-top: auto;

        .post-user-info-bottom {
          display: flex;
          align-items: center;
          gap: $uni-spacing-row-sm;
          margin-bottom: $uni-spacing-col-sm;
          cursor: pointer;
          position: relative;

          .user-avatar-small {
            width: 40rpx;
            height: 40rpx;
            border-radius: $uni-border-radius-circle;
            flex-shrink: 0;
          }

          .user-info-content {
            flex: 1;
            
            .username-row {
              display: flex;
              align-items: center;
              gap: $uni-spacing-row-sm;
              margin-bottom: $uni-spacing-col-xs;

              .live-badge-small {
                display: flex;
                align-items: center;
                gap: 4rpx;
                background: linear-gradient(135deg, $app-primary-color 0%, mix($app-primary-color, #FF6B7A, 70%) 100%);
                border-radius: $uni-border-radius-pill;
                padding: 4rpx 8rpx;
                box-shadow: 0 2rpx 8rpx rgba(255, 90, 95, 0.3);
                
                .live-text {
                  font-size: 20rpx;
                  color: $uni-text-color-inverse;
                  font-weight: 500;
                  line-height: 1;
                }
              }

              .post-username-small {
                font-size: $uni-font-size-sm;
                color: $uni-text-color;
                font-weight: 500;
              }
            }

            .post-time-small {
              font-size: $uni-font-size-xs;
              color: $uni-text-color-grey;
            }
          }
        }

        .post-stats {
          display: flex;
          justify-content: flex-start;
          gap: 40rpx;

          .stat-item {
            display: flex;
            align-items: center;
            gap: $uni-spacing-row-xs;

            text {
              font-size: $uni-font-size-sm;
              color: $uni-text-color-grey;
            }

            &.active text {
              color: $app-primary-color;
            }
          }
        }
      }
    }
  }

  // Multiple images layout (traditional layout)
  .multiple-images-layout {
    // Post Header
    .post-header {
      display: flex;
      align-items: center;
      margin-bottom: $uni-spacing-col-base;
      position: relative;

      .post-avatar {
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        border-radius: $uni-border-radius-circle;
        margin-right: $uni-spacing-row-base;
        cursor: pointer;
      }

      .post-user-info {
        cursor: pointer;

        .username-with-live {
          display: flex;
          align-items: center;
          gap: $uni-spacing-row-sm;
          margin-bottom: $uni-spacing-col-xs;

          .live-badge-large {
            display: flex;
            align-items: center;
            gap: 4rpx;
            background: linear-gradient(135deg, $app-primary-color 0%, mix($app-primary-color, #FF6B7A, 70%) 100%);
            border-radius: $uni-border-radius-pill;
            padding: 6rpx 12rpx;
            box-shadow: 0 2rpx 8rpx rgba(255, 90, 95, 0.3);

            .live-text {
              font-size: $uni-font-size-xs;
              color: $uni-text-color-inverse;
              font-weight: 500;
              line-height: 1;
            }
          }

          .post-username {
            font-size: $uni-font-size-medium;
            color: $uni-text-color;
            font-weight: 500;
          }
        }

        .post-time {
          font-size: $uni-font-size-sm;
          color: $uni-text-color-grey;
          display: block;
        }
      }

      .header-stats {
        display: flex;
        align-items: center;
        gap: 20rpx;
        margin-left: auto;
        margin-right: $uni-spacing-row-base;

        .stat-item {
          display: flex;
          align-items: center;
          gap: $uni-spacing-row-xs;

          text {
            font-size: $uni-font-size-xs;
            color: $uni-text-color-grey;
          }

          &.active text {
            color: $app-primary-color;
          }
        }
      }

      .post-actions {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    // Post Content
    .post-content {
      .post-title {
        font-size: $uni-font-size-lg;
        color: $uni-text-color;
        font-weight: 500;
        line-height: 1.4;
        margin-bottom: $uni-spacing-col-base;
      }

      // Media Content
      .post-media {
        width: 100%;

        // Images Container
        .images-container {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;

          .post-image {
            border-radius: $uni-border-radius-sm;
            background-color: $uni-bg-color-light-grey;

            &.image-count-2 {
              width: 49%;
              height: 260rpx;
            }

            &.image-count-3 {
              width: 32%;
              height: 160rpx;
            }

            .image {
              width: 100%;
              height: 100%;
              border-radius: 12rpx;
            }
          }
        }
      }
    }
  }

  // Comments Section
  .comments-section {
    margin-top: $uni-spacing-col-base;

    .section-divider {
      height: 1rpx;
      background-color: $uni-border-color-light;
      margin: $uni-spacing-col-base 0;
    }

    .comment-item {
      .comment-content {
        display: flex;
        align-items: center;
        gap: $uni-spacing-row-sm;
        padding: $uni-spacing-col-xs 0;

        .comment-user-info {
          display: flex;
          align-items: center;
          gap: $uni-spacing-row-sm;
          cursor: pointer;
          position: relative;

          .comment-avatar {
            width: $uni-img-size-sm;
            height: $uni-img-size-sm;
            border-radius: $uni-border-radius-circle;
            flex-shrink: 0;
          }

          .comment-user-content {
            display: flex;
            align-items: center;
            gap: $uni-spacing-row-sm;
            
            .live-badge-comment {
              display: flex;
              align-items: center;
              gap: 4rpx;
              background: linear-gradient(135deg, $app-primary-color 0%, mix($app-primary-color, #FF6B7A, 70%) 100%);
              border-radius: $uni-border-radius-pill;
              padding: 4rpx 8rpx;
              box-shadow: 0 2rpx 8rpx rgba(255, 90, 95, 0.3);

              .live-text {
                font-size: 20rpx;
                color: $uni-text-color-inverse;
                font-weight: 500;
                line-height: 1;
              }
            }

            .comment-username {
              color: $app-secondary-color;
              font-size: $uni-font-size-sm;
              font-weight: 500;
            }
          }
        }

        .comment-text {
          color: $uni-text-color;
          font-size: $uni-font-size-base;
          flex: 1;
        }
      }
    }

    .more-comments {
      margin-top: $uni-spacing-col-sm;
      font-size: $uni-font-size-sm;
      color: $uni-text-color-grey;
      text-align: center;
    }
  }
}
</style>