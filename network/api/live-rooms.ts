import {Protobuf} from "@/proto/proto.js";
import {Http} from "@/Lib/Http";
import {Socket} from "@/Lib/Socket";
export const AppsLiveApiLiveRoomsApiReady = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto,showErr:boolean = true): Promise<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto> => {
    return Http.callApi<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto>("apps/live/api/live-rooms/ready", params, showErr,'直播间准备开播失败',Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, 0);
}
export const AppsLiveApiLiveRoomsApiLists = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto,showErr:boolean = true): Promise<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsListsProto> => {
    return Http.callApi<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsListsProto>("apps/live/api/live-rooms/lists", params, showErr,'获取直播间表列表数据失败',Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, Protobuf.Wenyuehui.LiveRooms.LiveRoomsListsProto, 0);
}
export const AppsLiveApiLiveRoomsApiDetail = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto,showErr:boolean = true): Promise<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto> => {
    return Http.callApi<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto>("apps/live/api/live-rooms/detail", params, showErr,'查看直播间表详情失败',Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, 0);
}
export const AppsLiveApiLiveRoomsApiDetailByUserId = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto,showErr:boolean = true): Promise<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto> => {
    return Http.callApi<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto>("apps/live/api/live-rooms/detail-by-user-id", params, showErr,'查看直播间表详情失败',Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, 0);
}
export const AppsLiveApiLiveRoomsApiDetailByUser = (params: null,showErr:boolean = true): Promise<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto> => {
    return Http.callApi<Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto>("apps/live/api/live-rooms/detail-by-user", params, showErr,'查看直播间表详情失败',null, Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, 0);
}
export const AppsLiveApiLiveRoomsApiSwitchCommentsSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/switch-comments",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '设置是否允许评论失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiSwitchCommentsSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/switch-comments",callback);
}

export const AppsLiveApiLiveRoomsApiSwitchCommentsSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/switch-comments",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiSwitchGiftSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/switch-gift",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '设置是否允许礼物失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiSwitchGiftSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/switch-gift",callback);
}

export const AppsLiveApiLiveRoomsApiSwitchGiftSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/switch-gift",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiSwitchLianMaiSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/switch-lian-mai",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '设置是否允许连麦失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiSwitchLianMaiSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/switch-lian-mai",callback);
}

export const AppsLiveApiLiveRoomsApiSwitchLianMaiSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/switch-lian-mai",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiRequestLianMaiSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/request-lian-mai",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '请求连麦失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiRequestLianMaiSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/request-lian-mai",callback);
}

export const AppsLiveApiLiveRoomsApiRequestLianMaiSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/request-lian-mai",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiUserQuitLianMaiSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/user-quit-lian-mai",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '退出连麦失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiUserQuitLianMaiSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/user-quit-lian-mai",callback);
}

export const AppsLiveApiLiveRoomsApiUserQuitLianMaiSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/user-quit-lian-mai",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiReplyLianMaiSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/reply-lian-mai",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '回复连麦失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiReplyLianMaiSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/reply-lian-mai",callback);
}

export const AppsLiveApiLiveRoomsApiReplyLianMaiSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/reply-lian-mai",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiKickOutLianMaiSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/kick-out-lian-mai",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '踢出连麦失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiKickOutLianMaiSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/kick-out-lian-mai",callback);
}

export const AppsLiveApiLiveRoomsApiKickOutLianMaiSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/kick-out-lian-mai",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiSwitchRecord = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto,showErr:boolean = true): Promise<Protobuf.Common.ISuccess> => {
    return Http.callApi<Protobuf.Common.ISuccess>("apps/live/api/live-rooms/switch-record", params, showErr,'设置是否允许录制失败',Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, Protobuf.Common.Success, 0);
}
export const AppsLiveApiLiveRoomsApiStartSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/start",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '开播失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiStartSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/start",callback);
}

export const AppsLiveApiLiveRoomsApiStartSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/start",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiOfflineSocketOn = (showErr:boolean = true, callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().on({        
        url: "apps/live/api/live-rooms/offline",
        responseProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto,
        showError: showErr,
        errorMessage: '下播失败',
        callback: callback,
    })
}

export const AppsLiveApiLiveRoomsApiOfflineSocketOff = (callback: (res: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => void) => {
    Socket.getInstance().off("apps/live/api/live-rooms/offline",callback);
}

export const AppsLiveApiLiveRoomsApiOfflineSocketSend = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto) => {
    Socket.getInstance().send({
        url: "apps/live/api/live-rooms/offline",
        params: params,
        requestProtoBuf: Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto
    })
}

export const AppsLiveApiLiveRoomsApiDelete = (params: Protobuf.Wenyuehui.LiveRooms.ILiveRoomsProto,showErr:boolean = true): Promise<Protobuf.Common.ISuccess> => {
    return Http.callApi<Protobuf.Common.ISuccess>("apps/live/api/live-rooms/delete", params, showErr,'删除直播间表失败',Protobuf.Wenyuehui.LiveRooms.LiveRoomsProto, Protobuf.Common.Success, 0);
}
